#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证筛选结果的脚本
"""

import pandas as pd

def verify_filtered_data():
    """验证筛选结果"""
    try:
        # 读取筛选后的数据
        df = pd.read_excel("readAndsend/filtered_data.xlsx", engine='openpyxl')
        
        print("=" * 60)
        print("筛选结果验证")
        print("=" * 60)
        print(f"总行数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print(f"列名: {list(df.columns)}")
        
        print("\n系统分组统计:")
        print(df['系统分组'].value_counts())
        
        print("\n应用名称后缀统计:")
        suffix_counts = {'XZ': 0, 'NJ': 0, 'LGY': 0}
        for app_name in df['应用名称']:
            app_name_upper = str(app_name).upper()
            if app_name_upper.endswith('XZ'):
                suffix_counts['XZ'] += 1
            elif app_name_upper.endswith('NJ'):
                suffix_counts['NJ'] += 1
            elif app_name_upper.endswith('LGY'):
                suffix_counts['LGY'] += 1
        
        for suffix, count in suffix_counts.items():
            print(f"  以{suffix}结尾: {count} 个")
        
        print(f"\n前10行数据:")
        print(df[['应用名称', '系统分组']].head(10))
        
        print(f"\n后10行数据:")
        print(df[['应用名称', '系统分组']].tail(10))
        
        return True
        
    except Exception as e:
        print(f"验证时出错: {e}")
        return False

if __name__ == "__main__":
    verify_filtered_data()
