#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本功能：
1. 读取1.xlsx文件
2. 筛选系统分组列中值为"计费中心"的数据
3. 筛选应用名称列中结尾是XZ、NJ、LGY的数据（不区分大小写）
4. 将筛选结果保存到新的Excel文件
"""

import pandas as pd
import os
import sys

def read_excel_and_filter(input_file, output_file):
    """
    读取Excel文件并根据条件筛选数据
    
    Args:
        input_file (str): 输入Excel文件路径
        output_file (str): 输出Excel文件路径
    """
    try:
        # 读取Excel文件
        print(f"正在读取文件: {input_file}")
        
        # 尝试读取.xls文件
        if input_file.endswith('.xls'):
            df = pd.read_excel(input_file, engine='xlrd')
        else:
            df = pd.read_excel(input_file, engine='openpyxl')
        
        print(f"成功读取文件，共 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")
        
        # 显示前几行数据以便确认结构
        print("\n前5行数据预览:")
        print(df.head())
        
        # 检查是否存在所需的列
        required_columns = ['系统分组', '应用名称']
        missing_columns = []
        
        for col in required_columns:
            if col not in df.columns:
                # 尝试找到相似的列名
                similar_cols = [c for c in df.columns if col in c or c in col]
                if similar_cols:
                    print(f"警告: 未找到列 '{col}'，但找到相似列: {similar_cols}")
                else:
                    missing_columns.append(col)
        
        if missing_columns:
            print(f"错误: 缺少必需的列: {missing_columns}")
            print("请检查Excel文件的列名是否正确")
            return False
        
        # 筛选条件1: 系统分组列中的值是"计费中心"
        print("\n正在应用筛选条件...")
        condition1 = df['系统分组'] == '计费中心'
        filtered_df = df[condition1]
        print(f"筛选条件1 (系统分组='计费中心'): 剩余 {len(filtered_df)} 行")
        
        # 筛选条件2: 应用名称列中结尾是XZ、NJ、LGY（不区分大小写）
        def check_app_name_suffix(app_name):
            """检查应用名称是否以指定后缀结尾（不区分大小写）"""
            if pd.isna(app_name):
                return False
            app_name_upper = str(app_name).upper()
            return app_name_upper.endswith('XZ') or app_name_upper.endswith('NJ') or app_name_upper.endswith('LGY')
        
        condition2 = filtered_df['应用名称'].apply(check_app_name_suffix)
        final_filtered_df = filtered_df[condition2]
        print(f"筛选条件2 (应用名称结尾是XZ/NJ/LGY): 剩余 {len(final_filtered_df)} 行")
        
        if len(final_filtered_df) == 0:
            print("警告: 没有找到符合条件的数据")
            return False
        
        # 显示筛选结果
        print(f"\n筛选结果预览:")
        print(final_filtered_df[['系统分组', '应用名称']].head(10))
        
        # 保存到新的Excel文件
        print(f"\n正在保存结果到: {output_file}")
        final_filtered_df.to_excel(output_file, index=False, engine='openpyxl')
        print(f"成功保存 {len(final_filtered_df)} 行数据到 {output_file}")
        
        # 显示保存的数据统计
        print(f"\n数据统计:")
        print(f"- 原始数据行数: {len(df)}")
        print(f"- 筛选后行数: {len(final_filtered_df)}")
        print(f"- 筛选比例: {len(final_filtered_df)/len(df)*100:.2f}%")
        
        return True
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
        return False
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return False

def main():
    """主函数"""
    # 文件路径
    input_file = "readAndsend/1.xls"  # 输入文件
    output_file = "readAndsend/filtered_data.xlsx"  # 输出文件
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        # 尝试查找1.xlsx文件
        xlsx_file = "readAndsend/1.xlsx"
        if os.path.exists(xlsx_file):
            input_file = xlsx_file
            print(f"找到1.xlsx文件，使用: {input_file}")
        else:
            print(f"错误: 找不到输入文件 {input_file} 或 {xlsx_file}")
            return
    
    print("=" * 50)
    print("Excel数据筛选脚本")
    print("=" * 50)
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"筛选条件:")
    print(f"  1. 系统分组 = '计费中心'")
    print(f"  2. 应用名称结尾是 'XZ'、'NJ'、'LGY'（不区分大小写）")
    print("=" * 50)
    
    # 执行筛选
    success = read_excel_and_filter(input_file, output_file)
    
    if success:
        print(f"\n✅ 处理完成！结果已保存到: {output_file}")
    else:
        print(f"\n❌ 处理失败，请检查文件和数据格式")

if __name__ == "__main__":
    main()
