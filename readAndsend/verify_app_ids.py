#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证应用标识分组结果的脚本
"""

import pandas as pd
import os

def verify_app_ids():
    """验证应用标识分组结果"""
    try:
        print("=" * 60)
        print("应用标识分组结果验证")
        print("=" * 60)
        
        # 读取原始筛选数据
        df = pd.read_excel("readAndsend/filtered_data.xlsx", engine='openpyxl')
        print(f"原始数据总行数: {len(df)}")
        
        # 统计包含controlNode的应用
        control_node_apps = df[df['应用名称'].str.contains('controlnode', case=False, na=False)]
        other_apps = df[~df['应用名称'].str.contains('controlnode', case=False, na=False)]
        
        print(f"包含controlNode的应用: {len(control_node_apps)} 个")
        print(f"不包含controlNode的应用: {len(other_apps)} 个")
        
        # 验证controlNode.txt文件
        control_node_file = "readAndsend/controlNode.txt"
        if os.path.exists(control_node_file):
            print(f"\n验证 {control_node_file}:")
            with open(control_node_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            total_ids = 0
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if line:
                    ids = line.split(',')
                    total_ids += len(ids)
                    print(f"  第{i}行: {len(ids)} 个应用标识")
            
            print(f"  总计: {total_ids} 个应用标识")
            print(f"  预期: {len(control_node_apps)} 个应用标识")
            print(f"  匹配: {'✅' if total_ids == len(control_node_apps) else '❌'}")
        
        # 验证out.txt文件
        out_file = "readAndsend/out.txt"
        if os.path.exists(out_file):
            print(f"\n验证 {out_file}:")
            with open(out_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            total_ids = 0
            for i, line in enumerate(lines, 1):
                line = line.strip()
                if line:
                    ids = line.split(',')
                    total_ids += len(ids)
                    print(f"  第{i}行: {len(ids)} 个应用标识")
            
            print(f"  总计: {total_ids} 个应用标识")
            print(f"  预期: {len(other_apps)} 个应用标识")
            print(f"  匹配: {'✅' if total_ids == len(other_apps) else '❌'}")
        
        # 显示包含controlNode的应用示例
        print(f"\n包含controlNode的应用示例:")
        for i, (_, row) in enumerate(control_node_apps.head(10).iterrows(), 1):
            print(f"  {i}. {row['应用名称']} -> {row['应用标识']}")
        
        if len(control_node_apps) > 10:
            print(f"  ... (共{len(control_node_apps)}个)")
        
        return True
        
    except Exception as e:
        print(f"验证时出错: {e}")
        return False

if __name__ == "__main__":
    verify_app_ids()
