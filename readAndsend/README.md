# 应用数据处理脚本

## 功能说明

这个Python脚本用于：

1. 读取 `app.txt` 文件中的应用名称
2. 使用 `-` 分割应用名称，获取第二个元素作为 `pipelineName` 参数
3. 调用API获取应用详细信息
4. 筛选出以 `JP` 开头且以 `NJ`、`XZ`、`LYG` 结尾的应用
5. 将结果保存到Excel文件中

## 文件说明

- `app.txt` - 包含应用名称列表的文本文件
- `process_app_data.py` - 完整版脚本，处理所有应用
- `test_script.py` - 测试版脚本，只处理前5个应用
- `requirements.txt` - Python依赖包列表
- `README.md` - 使用说明文档

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install requests pandas openpyxl
```

## 使用方法

### 测试运行（推荐先运行）

```bash
python test_script.py
```

这会处理前5个应用，生成 `test_filtered_data.xlsx` 文件。

### 完整运行

```bash
python process_app_data.py
```

这会处理所有109个应用，生成 `filtered_app_data.xlsx` 文件。

## API配置

脚本使用以下API配置：

- **URL**: `http://*************:8181/api/compile/compileManage/getDetailInfo`
- **方法**: POST
- **认证**: Bearer Token（已在脚本中配置）

## 筛选条件

脚本会筛选满足以下条件的应用：

- `appName` 以 `JP` 开头
- `appName` 以 `NJ`、`XZ` 或 `LYG` 结尾

## 输出格式

生成的Excel文件包含以下列：

- `txt中的应用名称` - 来自app.txt文件的原始应用名称
- `pipelineName` - 从应用名称提取的pipeline名称
- `id` - 应用的唯一标识符
- `appName` - API返回的应用名称

## 示例输出

从测试运行中，找到了21条符合条件的记录，例如：

- JP-dealAttr-SQ -> boss_rate_dealattr -> JP-dealAttr-LYG
- JP-loadLog-SQ -> boss_rate_loadlog -> JP-loadLog-NJ
- JP-gprsTrade-SQ -> boss_rate_gprstrade -> JP-gprsTrade-XZ

## 注意事项

1. 确保网络连接正常，能够访问API地址
2. API Token有时效性，如果请求失败可能需要更新Token
3. 完整运行可能需要较长时间，建议先运行测试版本
4. 如果中途中断，可以重新运行脚本

## 错误处理

脚本包含以下错误处理：

- 网络请求超时处理
- API响应错误处理
- 文件读写错误处理
- 键盘中断处理（Ctrl+C）

## 技术细节

- 使用 `requests` 库进行HTTP请求
- 使用 `pandas` 和 `openpyxl` 处理Excel文件
- 支持分页请求（如果API返回多页数据）
- 包含详细的进度显示和日志输出
