#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本功能：
1. 读取app.txt文件
2. 使用'-'拆分，获取第二个元素作为pipelineName
3. 发送API请求获取数据
4. 筛选以JP开头且以NJ、XZ、LYG结尾的appName
5. 将结果保存到Excel文件
"""

import requests
import json
import pandas as pd
import os
import sys

def read_app_file(file_path):
    """读取app.txt文件并提取pipelineName"""
    pipeline_names = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            line = line.strip()
            if line and '-' in line:
                parts = line.split('-')
                if len(parts) >= 2:
                    pipeline_name = parts[1]  # 获取第二个元素
                    pipeline_names.append((line, pipeline_name))
        
        return pipeline_names
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return []
    except Exception as e:
        print(f"读取文件时出错：{e}")
        return []

def send_api_request(pipeline_name, page_num=1):
    """发送API请求获取数据"""
    url = "http://*************:8181/api/compile/compileManage/getDetailInfo"
    
    headers = {
        "authorization": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2luZm8iOnsiaWQiOiI3MDAwMDAwMTciLCJuYW1lIjoi546L5LqR57-UIiwic3BlbGwiOiJ3YW5neXVueGlhbmciLCJzaG9ydE5hbWUiOiJ3eXgiLCJhY2NvdW50IjoiMDY5NTgiLCJwYXNzd29yZCI6bnVsbCwibW9iaWxlIjoiIiwiZW1haWwiOiJ3YW5neXVueEBuZXdsYW5kLmNvbS5jbiIsImdlbmRlciI6bnVsbCwiYWN0b3JJZCI6IjkwMDAwMDAxNyIsIm9yaWdpbkZsYWciOiJMT0NBTCIsIm91dHRlcklkIjpudWxsLCJhcHBJZCI6IjJiMGNhMzNmZjNmMTQ4ZTA5NDg5ZWMyYWNlYTczMDhiIiwib2JqU3RhdHVzIjowLCJjcmVhdGVEYXRlIjoxNTc4MjgyMzk3MDAwLCJjcmVhdGVVc2VyIjpudWxsLCJ1cGRhdGVEYXRlIjoxNzQ5MjA3OTQyMDAwLCJ1cGRhdGVVc2VyIjpudWxsLCJpc0luaXRQd2QiOjAsImNhblVwZGF0ZVB3ZCI6MSwibWVudXMiOm51bGwsImRlcGFydG1lbnRJZCI6ImYyYmNhNmE4MzUyYzRjMzQ5NmM3ZGFlZGRmMjYzYWE0IiwiZGVwYXJ0bWVudEFjdG9ySWQiOiJiOTg5YWM1NDYyMDU0ZmNhYjQ5MGNkMDBjMmQ3OTczMSIsImRlcGFydG1lbnROYW1lIjoiQk9TU-S6p-WTgTLpg6giLCJvcmdhbml6YXRpb25OYW1lIjpudWxsLCJvcmdhbml6YXRpb25QaG90byI6bnVsbCwiaG9tZVBhZ2VQYXRoIjpudWxsLCJob21lUGFnZVRpdGxlIjpudWxsfSwidXNlcl9uYW1lIjoiMDY5NTgiLCJzY29wZSI6WyJ1c2VyIl0sImV4cCI6MTc0OTI2NDI1NCwiYXV0aG9yaXRpZXMiOlsi6ZyA5rGC6aOO6Zmp55m95ZCN5Y2VIiwi5byA5Y-R5Lq65ZGYIiwi57uE6ZW_Iiwi6YOo572y566h55CG6K-V55SoIl0sImp0aSI6IjIxNGQxMWZkLTkzYzAtNDVjNi1hOWYwLWQ5ODM2Nzc1NGMyNiIsImNsaWVudF9pZCI6InRlc3QifQ.BoLRnC6zeVb2TV7haK_2-YNm7UJnBNfolyRwuLiHmBo",
        "content-type": "application/json;charset=UTF-8",
        "host": "*************:8181",
        "origin": "http://*************:8181",
        "referer": "http://*************:8181/",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
    
    payload = {
        "pipelineName": pipeline_name,
        "envName": "",
        "envKey": "",
        "appCode": "",
        "isGrey": "",
        "range": "2",
        "codeIdList": ["5829","5831","5834","550bbddce42d4ca2ad695ce6c0572b5b","c03064170b514ae6bbf31874933073d2","c93df6c4e0d94f5e8325fdbea8f28a84","1dd65d777723463c895d7944985f8f7c","619d4a4c73644f23afd5aa59d43ee7f8","9c22346ba3a24c309ee8fc170d8b2fc3","1a7523db40ae4518ac6d12998177bd75","cc591c70d8ca4098b46a489c16760c75","f170902b28dd4cb8841d33b7f4e5fb23","141442d59071477fab2cf2af10e1f060","2f690a9a43cc4486a965d1dfe836f98c","687d6de30b204e80ab2ab3febdecf457","a8686fc7500044abb3d9dbc4ac9d3dd8","ed553e507cf04c85af415d08e3d95e93","fd0a9bd8040848758722fd5926e3d455","5aca6b0ab08f42b59a38d85e058a5dd5","bdec78ee01634762a3cc2dfed04915e7","cfa10324caec440b93988de684f1513d","7273ae020e424a1aa351dae975783296","7d8fe507f157410ab0e46eafd2f55a0d","d34391f3dbfb4606a0c9e2ef012b37eb","c72835d4bc2b4db1bcd90156ebeb60ed","e1d01222eb514872a424635e1b499568","f9e74b32bed047998f4d08819ed370c7","603ad01e590149c8afda1a61692b26c8","1fa2d4acc708402d93adffb6e881bab2","20103cf2cd4043ce94c20fc01a4a514e","034f0229d1194c41bf284c494657fa77","097d61340d5b48fca40bcb0a8495e60d","feea5a93148c431990bfaa3ce51d354c","fe3f2d7d553f421ba116cd85a8e21dfc","37ca48833a3b4de9b3e72075d90ddfde","de1a7f4f44424d01b22e266d31241b09","4467","4465","4464"],
        "pageNum": page_num,
        "pageSize": 500
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API请求失败：{e}")
        return None

def filter_app_names(records):
    """筛选以JP开头且以NJ、XZ、LYG结尾的appName"""
    filtered_data = []
    
    for record in records:
        app_name = record.get('appName', '')
        if app_name.startswith('JP') and (app_name.endswith('NJ') or app_name.endswith('XZ') or app_name.endswith('LYG')):
            filtered_data.append(record)
    
    return filtered_data

def save_to_excel(data, filename):
    """保存数据到Excel文件"""
    if not data:
        print("没有符合条件的数据需要保存")
        return
    
    df = pd.DataFrame(data)
    
    # 选择需要的列
    columns_to_save = ['txt中的应用名称', 'pipelineName', 'id', 'appName']
    df_filtered = df[columns_to_save]
    
    try:
        df_filtered.to_excel(filename, index=False, engine='openpyxl')
        print(f"数据已保存到 {filename}")
    except Exception as e:
        print(f"保存Excel文件时出错：{e}")

def main():
    """主函数"""
    app_file_path = "readAndsend/app.txt"

    # 读取app.txt文件
    print("正在读取app.txt文件...")
    pipeline_data = read_app_file(app_file_path)

    if not pipeline_data:
        print("没有找到有效的pipeline数据")
        return

    print(f"找到 {len(pipeline_data)} 个pipeline")

    all_filtered_data = []

    # 为每个pipeline发送请求
    for i, (app_name, pipeline_name) in enumerate(pipeline_data, 1):
        print(f"\n[{i}/{len(pipeline_data)}] 正在处理: {app_name} -> {pipeline_name}")

        try:
            # 发送第一页请求
            response_data = send_api_request(pipeline_name, 1)

            if not response_data:
                print(f"获取 {pipeline_name} 数据失败")
                continue

            # 只打印简要信息，不打印完整响应
            if response_data.get('respResult') != '1':
                print(f"API返回错误: {response_data}")
                continue

            resp_data = response_data.get('respData', {})
            records = resp_data.get('records', [])
            total_pages = resp_data.get('pages', 1)
            total_records = resp_data.get('total', 0)

            print(f"  - 总记录数: {total_records}, 总页数: {total_pages}")

            # 筛选符合条件的数据
            filtered_records = filter_app_names(records)

            # 为筛选出的记录添加txt中的应用名称
            for record in filtered_records:
                record['txt中的应用名称'] = app_name

            all_filtered_data.extend(filtered_records)

            if filtered_records:
                print(f"  - 第1页找到 {len(filtered_records)} 条符合条件的记录")

            # 如果有多页，继续请求其他页
            if total_pages > 1:
                for page in range(2, total_pages + 1):
                    print(f"  - 正在获取第 {page} 页数据...")
                    page_response = send_api_request(pipeline_name, page)

                    if page_response and page_response.get('respResult') == '1':
                        page_records = page_response.get('respData', {}).get('records', [])
                        page_filtered = filter_app_names(page_records)

                        # 为筛选出的记录添加txt中的应用名称
                        for record in page_filtered:
                            record['txt中的应用名称'] = app_name

                        all_filtered_data.extend(page_filtered)

                        if page_filtered:
                            print(f"  - 第{page}页找到 {len(page_filtered)} 条符合条件的记录")
                    else:
                        print(f"  - 第{page}页请求失败")

        except KeyboardInterrupt:
            print(f"\n用户中断，已处理 {i-1} 个pipeline")
            break
        except Exception as e:
            print(f"处理 {pipeline_name} 时出错: {e}")
            continue

    # 保存到Excel
    if all_filtered_data:
        print(f"\n总共找到 {len(all_filtered_data)} 条符合条件的记录")
        save_to_excel(all_filtered_data, "readAndsend/filtered_app_data.xlsx")

        # 打印汇总信息
        print("\n符合条件的应用:")
        for data in all_filtered_data:
            print(f"- {data['txt中的应用名称']} -> {data['pipelineName']} -> {data['appName']} (ID: {data['id']})")
    else:
        print("\n没有找到符合条件的数据")

if __name__ == "__main__":
    main()
