#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：只处理前5个pipeline来验证功能
"""

import requests
import json
import pandas as pd

def read_app_file(file_path, limit=5):
    """读取app.txt文件并提取pipelineName，限制数量"""
    pipeline_names = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        count = 0
        for line in lines:
            if count >= limit:
                break
                
            line = line.strip()
            if line and '-' in line:
                parts = line.split('-')
                if len(parts) >= 2:
                    pipeline_name = parts[1]  # 获取第二个元素
                    pipeline_names.append((line, pipeline_name))
                    count += 1
        
        return pipeline_names
    except FileNotFoundError:
        print(f"错误：找不到文件 {file_path}")
        return []
    except Exception as e:
        print(f"读取文件时出错：{e}")
        return []

def send_api_request(pipeline_name, page_num=1):
    """发送API请求获取数据"""
    url = "http://*************:8181/api/compile/compileManage/getDetailInfo"
    
    headers = {
        "authorization": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2luZm8iOnsiaWQiOiI3MDAwMDAwMTciLCJuYW1lIjoi546L5LqR57-UIiwic3BlbGwiOiJ3YW5neXVueGlhbmciLCJzaG9ydE5hbWUiOiJ3eXgiLCJhY2NvdW50IjoiMDY5NTgiLCJwYXNzd29yZCI6bnVsbCwibW9iaWxlIjoiIiwiZW1haWwiOiJ3YW5neXVueEBuZXdsYW5kLmNvbS5jbiIsImdlbmRlciI6bnVsbCwiYWN0b3JJZCI6IjkwMDAwMDAxNyIsIm9yaWdpbkZsYWciOiJMT0NBTCIsIm91dHRlcklkIjpudWxsLCJhcHBJZCI6IjJiMGNhMzNmZjNmMTQ4ZTA5NDg5ZWMyYWNlYTczMDhiIiwib2JqU3RhdHVzIjowLCJjcmVhdGVEYXRlIjoxNTc4MjgyMzk3MDAwLCJjcmVhdGVVc2VyIjpudWxsLCJ1cGRhdGVEYXRlIjoxNzQ5MjA3OTQyMDAwLCJ1cGRhdGVVc2VyIjpudWxsLCJpc0luaXRQd2QiOjAsImNhblVwZGF0ZVB3ZCI6MSwibWVudXMiOm51bGwsImRlcGFydG1lbnRJZCI6ImYyYmNhNmE4MzUyYzRjMzQ5NmM3ZGFlZGRmMjYzYWE0IiwiZGVwYXJ0bWVudEFjdG9ySWQiOiJiOTg5YWM1NDYyMDU0ZmNhYjQ5MGNkMDBjMmQ3OTczMSIsImRlcGFydG1lbnROYW1lIjoiQk9TU-S6p-WTgTLpg6giLCJvcmdhbml6YXRpb25OYW1lIjpudWxsLCJvcmdhbml6YXRpb25QaG90byI6bnVsbCwiaG9tZVBhZ2VQYXRoIjpudWxsLCJob21lUGFnZVRpdGxlIjpudWxsfSwidXNlcl9uYW1lIjoiMDY5NTgiLCJzY29wZSI6WyJ1c2VyIl0sImV4cCI6MTc0OTI2NDI1NCwiYXV0aG9yaXRpZXMiOlsi6ZyA5rGC6aOO6Zmp55m95ZCN5Y2VIiwi5byA5Y-R5Lq65ZGYIiwi57uE6ZW_Iiwi6YOo572y566h55CG6K-V55SoIl0sImp0aSI6IjIxNGQxMWZkLTkzYzAtNDVjNi1hOWYwLWQ5ODM2Nzc1NGMyNiIsImNsaWVudF9pZCI6InRlc3QifQ.BoLRnC6zeVb2TV7haK_2-YNm7UJnBNfolyRwuLiHmBo",
        "content-type": "application/json;charset=UTF-8",
        "host": "*************:8181",
        "origin": "http://*************:8181",
        "referer": "http://*************:8181/",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
    
    payload = {
        "pipelineName": pipeline_name,
        "envName": "",
        "envKey": "",
        "appCode": "",
        "isGrey": "",
        "range": "2",
        "codeIdList": ["5829","5831","5834","550bbddce42d4ca2ad695ce6c0572b5b","c03064170b514ae6bbf31874933073d2"],
        "pageNum": page_num,
        "pageSize": 500
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"API请求失败：{e}")
        return None

def filter_app_names(records):
    """筛选以JP开头且以NJ、XZ、LYG结尾的appName"""
    filtered_data = []
    
    for record in records:
        app_name = record.get('appName', '')
        if app_name.startswith('JP') and (app_name.endswith('NJ') or app_name.endswith('XZ') or app_name.endswith('LYG')):
            filtered_data.append(record)
    
    return filtered_data

def save_to_excel(data, filename):
    """保存数据到Excel文件"""
    if not data:
        print("没有符合条件的数据需要保存")
        return
    
    df = pd.DataFrame(data)
    
    # 选择需要的列
    columns_to_save = ['txt中的应用名称', 'pipelineName', 'id', 'appName']
    df_filtered = df[columns_to_save]
    
    try:
        df_filtered.to_excel(filename, index=False, engine='openpyxl')
        print(f"数据已保存到 {filename}")
    except Exception as e:
        print(f"保存Excel文件时出错：{e}")

def main():
    """主函数"""
    app_file_path = "readAndsend/app.txt"
    
    # 读取app.txt文件，只处理前5个
    print("正在读取app.txt文件（测试模式：只处理前5个）...")
    pipeline_data = read_app_file(app_file_path, limit=5)
    
    if not pipeline_data:
        print("没有找到有效的pipeline数据")
        return
    
    print(f"找到 {len(pipeline_data)} 个pipeline")
    
    all_filtered_data = []
    
    # 为每个pipeline发送请求
    for i, (app_name, pipeline_name) in enumerate(pipeline_data, 1):
        print(f"\n[{i}/{len(pipeline_data)}] 正在处理: {app_name} -> {pipeline_name}")
        
        try:
            # 发送第一页请求
            response_data = send_api_request(pipeline_name, 1)
            
            if not response_data:
                print(f"获取 {pipeline_name} 数据失败")
                continue
            
            if response_data.get('respResult') != '1':
                print(f"API返回错误: {response_data}")
                continue
            
            resp_data = response_data.get('respData', {})
            records = resp_data.get('records', [])
            total_records = resp_data.get('total', 0)
            
            print(f"  - 总记录数: {total_records}")
            
            # 筛选符合条件的数据
            filtered_records = filter_app_names(records)
            
            # 为筛选出的记录添加txt中的应用名称
            for record in filtered_records:
                record['txt中的应用名称'] = app_name
            
            all_filtered_data.extend(filtered_records)
            
            if filtered_records:
                print(f"  - 找到 {len(filtered_records)} 条符合条件的记录:")
                for record in filtered_records:
                    print(f"    * {record['appName']} (ID: {record['id']})")
            else:
                print(f"  - 没有找到符合条件的记录")
        
        except Exception as e:
            print(f"处理 {pipeline_name} 时出错: {e}")
            continue
    
    # 保存到Excel
    if all_filtered_data:
        print(f"\n总共找到 {len(all_filtered_data)} 条符合条件的记录")
        save_to_excel(all_filtered_data, "readAndsend/test_filtered_data.xlsx")
        
        # 打印汇总信息
        print("\n符合条件的应用汇总:")
        for data in all_filtered_data:
            print(f"- {data['txt中的应用名称']} -> {data['pipelineName']} -> {data['appName']} (ID: {data['id']})")
    else:
        print("\n没有找到符合条件的数据")

if __name__ == "__main__":
    main()
