#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本功能：
1. 读取筛选后的Excel文件
2. 根据应用名称是否包含"controlNode"（不区分大小写）进行分组
3. 获取应用标识，按照逗号分隔50个一组
4. 包含controlNode的写入controlNode.txt
5. 不包含controlNode的写入out.txt
"""

import pandas as pd
import os

def process_app_ids(input_file):
    """
    处理应用标识并分组保存
    
    Args:
        input_file (str): 输入Excel文件路径
    """
    try:
        # 读取Excel文件
        print(f"正在读取文件: {input_file}")
        df = pd.read_excel(input_file, engine='openpyxl')
        
        print(f"成功读取文件，共 {len(df)} 行数据")
        
        # 检查必需的列
        if '应用名称' not in df.columns or '应用标识' not in df.columns:
            print("错误: 文件中缺少'应用名称'或'应用标识'列")
            return False
        
        # 分组处理
        control_node_ids = []  # 包含controlNode的应用标识
        other_ids = []         # 不包含controlNode的应用标识
        
        print("\n正在分析应用名称...")
        
        for index, row in df.iterrows():
            app_name = str(row['应用名称']).strip()
            app_id = str(row['应用标识']).strip()
            
            # 跳过空值
            if pd.isna(row['应用名称']) or pd.isna(row['应用标识']):
                continue
            
            # 检查应用名称是否包含controlNode（不区分大小写）
            if 'controlnode' in app_name.lower():
                control_node_ids.append(app_id)
                print(f"  包含controlNode: {app_name} -> {app_id}")
            else:
                other_ids.append(app_id)
        
        print(f"\n分组结果:")
        print(f"  包含controlNode的应用: {len(control_node_ids)} 个")
        print(f"  不包含controlNode的应用: {len(other_ids)} 个")
        
        # 处理包含controlNode的应用标识
        if control_node_ids:
            print(f"\n正在处理包含controlNode的应用标识...")
            control_node_file = "readAndsend/controlNode.txt"
            write_ids_to_file(control_node_ids, control_node_file)
        else:
            print(f"\n没有找到包含controlNode的应用")
        
        # 处理不包含controlNode的应用标识
        if other_ids:
            print(f"\n正在处理不包含controlNode的应用标识...")
            other_file = "readAndsend/out.txt"
            write_ids_to_file(other_ids, other_file)
        else:
            print(f"\n没有找到不包含controlNode的应用")
        
        return True
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {input_file}")
        return False
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return False

def write_ids_to_file(app_ids, output_file):
    """
    将应用标识按50个一组写入文件
    
    Args:
        app_ids (list): 应用标识列表
        output_file (str): 输出文件路径
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            # 按50个一组分割
            for i in range(0, len(app_ids), 50):
                group = app_ids[i:i+50]
                # 用逗号连接
                line = ','.join(group)
                f.write(line + '\n')
                
                group_num = i // 50 + 1
                print(f"  第{group_num}组: {len(group)} 个应用标识")
        
        total_groups = (len(app_ids) + 49) // 50  # 向上取整
        print(f"  成功保存到 {output_file}")
        print(f"  总共 {len(app_ids)} 个应用标识，分为 {total_groups} 组")
        
        # 显示文件内容预览
        print(f"  文件内容预览:")
        with open(output_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines[:3], 1):  # 只显示前3行
                preview = line.strip()
                if len(preview) > 100:
                    preview = preview[:100] + "..."
                print(f"    第{i}行: {preview}")
            if len(lines) > 3:
                print(f"    ... (共{len(lines)}行)")
        
    except Exception as e:
        print(f"写入文件 {output_file} 时出错: {e}")

def main():
    """主函数"""
    # 输入文件路径
    input_file = "readAndsend/filtered_data.xlsx"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 找不到输入文件 {input_file}")
        print("请先运行excel_filter_script.py生成筛选后的数据")
        return
    
    print("=" * 60)
    print("应用标识分组处理脚本")
    print("=" * 60)
    print(f"输入文件: {input_file}")
    print(f"输出文件:")
    print(f"  - controlNode.txt (包含controlNode的应用)")
    print(f"  - out.txt (不包含controlNode的应用)")
    print(f"处理规则:")
    print(f"  - 按应用名称是否包含'controlNode'分组（不区分大小写）")
    print(f"  - 应用标识按50个一组，用逗号分隔")
    print("=" * 60)
    
    # 执行处理
    success = process_app_ids(input_file)
    
    if success:
        print(f"\n✅ 处理完成！")
        print(f"结果文件:")
        
        # 检查生成的文件
        control_node_file = "readAndsend/controlNode.txt"
        out_file = "readAndsend/out.txt"
        
        if os.path.exists(control_node_file):
            size = os.path.getsize(control_node_file)
            print(f"  - {control_node_file} ({size} 字节)")
        
        if os.path.exists(out_file):
            size = os.path.getsize(out_file)
            print(f"  - {out_file} ({size} 字节)")
    else:
        print(f"\n❌ 处理失败，请检查文件和数据格式")

if __name__ == "__main__":
    main()
